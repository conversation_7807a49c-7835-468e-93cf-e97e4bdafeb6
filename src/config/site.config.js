import queryString from 'query-string';

const prod = process.env.NODE_ENV === 'production';

let pObj = {};

// Hide console logs in production mode
if (prod) {
  console.log = () => {};
} else {
  // eslint-disable-next-line global-require
  // const qs = require('query-string');
  pObj = queryString.parse(window.location.search);
  console.log('pObj:', pObj);
}

// API and Socket URLs
// const apiDomain = process.env.REACT_APP_URL;
// const socketUrl = process.env.REACT_APP_SOCKET_URL;
const socketUrl = 'https://api.chillbaby-test.io';

export default {
  prod,
  siteName: 'ChillBaby',
  // apiUrl: `${apiDomain}/api/`,
  apiUrl: `https://api.chillbaby-test.io/api/`,
  // apiUrl: `http://192.168.0.136:1338/api/`,
  domainUrl: window.location.origin,
  sailsUrl: socketUrl,

  // Date format for whole site
  dateFormat: 'DD-MM-YYYY',

  // TinyMCE Editor's API key
  tinyEditorKey: 'is5kd3sab675bjhf8boldam9xjqtoa1pit0adf6q3ojm6124',
};
