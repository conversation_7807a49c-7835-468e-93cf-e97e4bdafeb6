/* eslint-disable no-shadow */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-unused-vars */
/* eslint-disable no-param-reassign */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useEffect, useState } from 'react';
import { Row, Col, Spin, Progress, DatePicker, Button, Tag } from 'antd';
import {
  CloseCircleOutlined,
  DownOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import LayoutWrapper from '@chill/components/utility/layoutWrapper';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';

// Icons
import groupIcon from '@chill/assets/images/new_icons/groupIcon.png';
import databaseIcon from '@chill/assets/images/new_icons/databaseIcon.png';
import lineChartIcon from '@chill/assets/images/new_icons/lineChartIcon.png';
import reloadTimeIcon from '@chill/assets/images/new_icons/reloadTimeIcon.png';
import noImgIcon from '@chill/assets/images/new_icons/no-Img.png';

import { isEmpty, isObject } from 'lodash';
import getApiData from '@chill/lib/helpers/apiHelper';
import { useHistory } from 'react-router-dom';
import { injectIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import moment from 'moment';
import LinkBox from '@chill/components/LinkBox';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import Popover from '@chill/components/uielements/popover';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Rectangle,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import Icon from '@ant-design/icons/lib/components/Icon';
import filterIcon from '@chill/assets/images/filterIcon';
import IsoWidgetsWrapper from './WidgetsWrapper';
import ReportsWidget from './Report/ReportWidget';
import StickerWidget from './Sticker/StickerWidget';
import { DashboardWrapper, WidgetWrapper } from './Widgets.styles';
import { styles } from './config';
import MapChart from './MapChart';
import { StickerWidgetWrapper } from './Sticker/StickerWidget.styles';
import ProgressBar from '../../components/ProgressBar/index';

/**
 *
 * @module Dashboard
 */

function Widgets() {
  const history = useHistory();

  const { rowStyle, colStyle } = basicStyle;
  const access = useSelector((state) => state.Auth.access);
  const accLoading = access.loading || false;
  const [dData, setdData] = useState({ data: {}, loading: true });
  const [durationVisible, setDurationVisibility] = useState(null);
  const [filterVisible, setFilterVisible] = useState(false);
  const [filter, setFilter] = useState('');
  const [compaignDate, setCompaignDate] = useState('');
  const [duration, setDuration] = useState({
    customerOverview: <IntlMessages id="dashboard.thisweek" />,
    trafficByLocation: <IntlMessages id="dashboard.thisweek" />,
    sessionProductType: <IntlMessages id="dashboard.thisweek" />,
  });
  const [cperiodType, setCPeriodType] = useState('this_week');
  const [rperiodType, setRPeriodType] = useState('week');
  const [sessionpType, setSessionpType] = useState('week');
  const [marketingFilter, setMarketingFilter] = useState('');
  const [toolTipContent, setToolTipContent] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    data: {},
    loading: true,
  });

  const FilterIcon = (props) => <Icon component={filterIcon} {...props} />;

  const imgUrl =
    isObject(dData.data.chatTickets) && dData.data.chatTickets.user_profile
      ? dData.data.chatTickets.user_profile
      : '';

  const TOP_BOXES = [
    {
      key: 'totalusers',
      text: 'dashboard.totalusers',
      icon: <img src={groupIcon} alt="icon" />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      percentage: !isEmpty(dData.data)
        ? dData.data?.userTileDetails?.userPercentageChange
        : 0,
      isUp: !isEmpty(dData.data)
        ? dData.data?.userTileDetails?.userStatus
        : false,
      count: !isEmpty(dData.data)
        ? dData.data?.userTileDetails?.userDifference
        : 0,
    },
    {
      key: 'totalRevenue',
      text: 'dashboard.totalRevenue',
      icon: <img src={databaseIcon} alt="icon" />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      percentage: !isEmpty(dashboardData.data.salesDetails)
        ? dashboardData.data.salesDetails.cntDifferencePercentage
        : 0,
      isUp:
        !isEmpty(dashboardData?.data) &&
        dashboardData?.data?.salesDetails?.cntStatus === 'increase',
      count: `$${
        !isEmpty(dashboardData.data.salesDetails)
          ? dashboardData.data.salesDetails.cntDifference
          : 0
      }`,
    },
    {
      key: 'churnRate',
      text: 'dashboard.churnRate',
      // link: '/contacts',
      icon: <img src={lineChartIcon} alt="icon" />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      percentage: !isEmpty(dashboardData.data.churnDeatils)
        ? dashboardData.data.churnDeatils.churnRate
        : 0,
      isUp: !isEmpty(dashboardData.data.churnDeatils)
        ? dashboardData.data.churnDeatils.trend
        : false,
      count: !isEmpty(dashboardData.data.churnDeatils)
        ? dashboardData.data.churnDeatils.churnedCnt
        : 0,
    },
    {
      key: 'sessionDuration',
      text: 'dashboard.sessionDuration',
      // link: '/contacts',
      icon: <img src={reloadTimeIcon} alt="icon" />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      percentage: !isEmpty(dData.data)
        ? dData.data?.sessionTileDetails?.sessionPercentageChange
        : 0,
      isUp: !isEmpty(dData.data)
        ? dData.data?.sessionTileDetails?.sessionStatus
        : false,
      count: !isEmpty(dData.data)
        ? dData.data?.sessionTileDetails?.sessionAvgDiff
        : 0,
    },
  ];

  function durationChange(type, isVisible) {
    if (isVisible) {
      setDurationVisibility(type);
    } else {
      setDurationVisibility(null);
    }
  }

  function filterChange() {
    setFilterVisible(!filterVisible);
  }

  /** Function to fetch dashboard's data
   * @function getDashboardData
   * @param {object} data customer_overview, user_by_region
   */

  async function getDashboardData(data = {}) {
    data.customer_overview = {
      period_type: cperiodType,
    };
    data.user_by_region = {
      period_type: rperiodType,
    };
    data.session_product_chart = {
      period_type: sessionpType,
    };
    data.marketing_filter = filter;
    data.campaign_date = compaignDate;
    setdData((pre) => ({ ...pre, loading: true }));
    try {
      const res = await getApiData('getDashboardData', data, 'post');
      if (res.success && !isEmpty(res.data)) {
        const data = {};
        const userByTime = res?.data?.userByTime ? res.data.userByTime : [];

        const customerOverview = res?.data?.customerOverview
          ? res.data.customerOverview.map((cst) => {
              if (cst.type === 'number_of_users') {
                cst.type = 'Number Of Users';
              } else if (cst.type === 'number_of_visits') {
                cst.type = 'Number Of Visits';
              }
              return cst;
            })
          : [];
        data.userByTime = userByTime;
        data.customerOverview = customerOverview;
        data.totalUsers = res.data.totalUsers;
        data.weeklySession = res.data.weeklySession;
        data.userByRegion = res.data.userByRegion;
        data.marketingCampaigns = res.data.marketingCampaigns;
        data.chatTickets = res.data.chatTickets;
        data.brandList = res.data.brandList;
        data.userTileDetails = res.data.userTileDetails;
        data.sessionTileDetails = res.data.sessionTileDetails;
        data.session_product_chart = res.data.session_product_chart;
        setdData((pre) => ({ ...pre, data, loading: false }));
      } else {
        setdData((pre) => ({ ...pre, loading: false }));
      }
    } catch (err) {
      setdData((pre) => ({ ...pre, loading: false }));
    }
  }

  /**
   * Function to fetch Total Revenue & Churn Rate data
   * This function retrieves dashboard metrics such as total revenue and churn rate from an API.
   * The data is then processed and updated in the application's state for display.
   *
   * @async
   * @function getDashboardDetails
   * @returns {void} - This function does not return a value; it updates the state directly.
   */
  async function getDashboardDetails() {
    try {
      const res = await getApiData('e-commerce/dashboard-data', {}, 'post');

      if (res.success && !isEmpty(res.data)) {
        const data = {};

        // Extract the total sales value from the API response and set it as totalRevenue
        data.salesDetails = res?.data?.tileDetails?.totalSalesDetails;
        data.churnDeatils = res?.data?.churnDeatils;

        // Update the component's state with the fetched data and set loading to false
        setDashboardData({ data, loading: false });
      } else {
        // If API response is unsuccessful or data is empty, reset the dashboard state
        setDashboardData({ data: {}, loading: false });
      }
    } catch (err) {
      console.log('err ===', err);
      setDashboardData({ data: {}, loading: false }); // Reset the dashboard state in case of an error
    }
  }

  useEffect(() => {
    getDashboardData();
    getDashboardDetails();
  }, [cperiodType, rperiodType, sessionpType, filter, compaignDate]);

  // Campaign Marketing Chart Columns
  const marketingCampaignCol = [
    {
      key: 'sort_created',
      title: <IntlMessages id="ID" />,
      dataIndex: 'id',
      rowKey: 'no',
      width: 50,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div style={{ fontSize: '12px', fontWeight: 600, color: '#262E3D' }}>
            <span className="mobile-lbl-val">#{text}</span>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="Name" />,
      dataIndex: 'campaign_name',
      rowKey: 'campaign_name',
      width: 50,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              // justifyContent: 'center',
              gap: 5,
            }}
          >
            <div
              style={{
                borderRadius: '10px',
                height: '44px',
                width: '44px',
                backgroundColor: '#32C5FF',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <img
                style={{
                  borderRadius: '4px',
                  backgroundColor: '#32C5FF',
                }}
                height="22px"
                src={noImgIcon}
                alt="icon"
              />
            </div>
            <div>
              <p style={{ fontSize: '12px', fontWeight: 600 }}>{text}</p>
              <p style={{ fontSize: '12px', fontWeight: 400 }}>
                {data.description}
              </p>
            </div>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="marketing.type" />,
      dataIndex: 'campaign_type',
      rowKey: 'campaign_type',
      width: 50,
      className: 'fullname-cell',
      // sorter: (a, b) => a.age - b.age,
      // sortDirections: ['descend'],
      render: (text) => {
        return (
          <div>
            <span style={{ fontSize: '14px', color: '#667085' }}>
              {text || '-'}
            </span>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="marketing.status" />,
      dataIndex: 'status',
      rowKey: 'status',
      width: 50,
      className: 'fullname-cell',
      // sorter: (a, b) => a.age - b.age,
      // sortDirections: ['descend'],
      render: (text) => {
        let color;
        let fontColor;
        if (text === 'paused') {
          color = '#FDF1E8';
          fontColor = 'rgba(228, 106, 17, 1)';
        } else if (text === 'active') {
          color = '#E8F8FD';
          fontColor = 'rgba(19, 178, 228, 1)';
        } else if (text === 'completed') {
          color = '#E7F4EE82';
          fontColor = 'rgba(1, 196, 154, 1)';
        }
        return (
          <>
            <Tag
              style={{
                padding: '4px 12px',
                borderRadius: 100,
                color: fontColor,
                fontFamily: 'Inter',
                fontWeight: 600,
                fontSize: 14,
                letterSpacing: '0.5%',
              }}
              key={text}
              color={color}
            >
              {text}
            </Tag>
          </>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="marketing.clicks" />,
      dataIndex: 'clicks',
      rowKey: 'clicks',
      width: 50,
      className: 'fullname-cell',
      // sorter: (a, b) => a.age - b.age,
      // sortDirections: ['descend'],
      render: (text) => {
        return (
          <div>
            <span style={{ fontSize: '14px', color: '#667085' }}>{text}</span>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="marketing.likes" />,
      dataIndex: 'likes',
      rowKey: 'likes',
      width: 50,
      className: 'fullname-cell',
      // sorter: (a, b) => a.age - b.age,
      // sortDirections: ['descend'],
      render: (text) => {
        return (
          <div>
            <div>
              <span style={{ fontSize: '14px', color: '#667085' }}>{text}</span>
            </div>
          </div>
        );
      },
    },
  ];

  const customerCareCol = [
    {
      key: 'sort_created',
      title: <IntlMessages id="ID" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 50,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span style={{ fontSize: '12px', fontWeight: 600 }}>
              {text || '-'}
            </span>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="customer.name" />,
      dataIndex: 'name',
      rowKey: 'name',
      width: 50,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <p style={{ fontSize: '12px', fontWeight: 600 }}>{text || '-'}</p>
            <p style={{ fontSize: '12px', fontWeight: 400 }}>
              {data.ticket || '-'}
            </p>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="common.date" />,
      dataIndex: 'createdAt',
      rowKey: 'createdAt',
      width: 50,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <div>
              <span
                style={{ fontSize: '14px', fontWeight: 500, color: '#667085' }}
              >
                {text ? moment(text).format('DD/MM/YY') : '-'}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="marketing.status" />,
      dataIndex: 'status',
      rowKey: 'status',
      width: 50,
      className: 'fullname-cell',
      render: (tags) => {
        let color;
        let fontColor;
        if (tags === 'New') {
          color = '#FDF1E8';
          fontColor = 'rgba(228, 106, 17, 1)';
        } else if (tags === 'Active') {
          color = '#E8F8FD';
          fontColor = 'rgba(19, 178, 228, 1)';
        } else if (tags === 'Resolved') {
          color = '#E7F4EE82';
          fontColor = 'rgba(1, 196, 154, 1)';
        }
        return (
          <>
            <Tag
              style={{
                padding: '4px 12px',
                borderRadius: 100,
                color: fontColor,
                fontFamily: 'Inter',
                fontWeight: 700,
                fontSize: 12,
                letterSpacing: '0.5%',
              }}
              key={tags}
              color={color}
            >
              {tags || '-'}
            </Tag>
          </>
        );
      },
    },
  ];

  const popoverArray = [
    { key: 'week', text: 'dashboard.lastweek' },
    { key: 'this_month', text: 'dashboard.thisMonth' },
    { key: 'month', text: 'dashboard.lastMonth' },
    { key: 'six_month', text: 'dashboard.lastSixMonth' },
    { key: 'year', text: 'dashboard.lastYear' },
  ];

  const renderPopover = (type = '') => (
    <WidgetWrapper>
      <div className="popMainDiv">
        {popoverArray.map((item) => (
          <div
            className="isoDropdownLink"
            onClick={() => {
              console.log('hello testing itm', item, type);
              if (type === 'custumerOverview') {
                setDuration({
                  ...duration,
                  customerOverview: <IntlMessages id={item.text} />,
                });
                setDurationVisibility(!durationVisible);
                setCPeriodType(item.key);
              } else if (type === 'trafficByLocation') {
                setDuration({
                  ...duration,
                  trafficByLocation: <IntlMessages id={item.text} />,
                });
                setDurationVisibility(!durationVisible);
                setRPeriodType(item.key);
              } else {
                setSessionpType(item.key);
                setDuration({
                  ...duration,
                  sessionProductType: <IntlMessages id={item.text} />,
                });
                setDurationVisibility(!durationVisible);
              }
            }}
            role="button"
            onKeyPress={() => {}}
            tabIndex="-1"
          >
            <IntlMessages id={item.text} />{' '}
          </div>
        ))}
      </div>
    </WidgetWrapper>
  );

  const colors = [
    'rgba(2, 181, 255, 1)',
    'rgba(0, 134, 255, 1)',
    'rgba(46, 46, 48, 1)',
  ];

  const marketingTypeArr = [
    { key: 'feed_post', text: 'Feed Post' },
    { key: 'in_app_message', text: 'In-App Messages' },
    { key: 'push_message', text: 'Push Message' },
  ];

  const marketingSaveArr = [
    { key: 'Live', text: 'Live' },
    { key: 'Delivered', text: 'Delivered' },
    { key: 'Expired', text: 'Expired' },
  ];

  const marketCampaignFilterContent = (
    <WidgetWrapper>
      <div className="popMainDiv">
        {filter && (
          <div
            className="isoDropdownLink"
            onClick={() => {
              setFilter(null);
              setMarketingFilter('Filters');
              setFilterVisible(!filterVisible);
            }}
            role="button"
            onKeyPress={() => {
              setFilter(null);
              setMarketingFilter('Filters');
              setFilterVisible(!filterVisible);
            }}
            tabIndex="-1"
          >
            Clear Filter
          </div>
        )}
        <div className="isoDropdownHeading" role="button" tabIndex="-1">
          Type
        </div>
        {marketingTypeArr.map((item) => (
          <div
            className="isoDropdownLink"
            onClick={() => {
              setFilter(item.key);
              setMarketingFilter(item.text);
              setFilterVisible(!filterVisible);
            }}
            role="button"
            tabIndex="-1"
          >
            {item.text}
          </div>
        ))}
        <div className="isoDropdownHeading" role="button" tabIndex="-1">
          Status
        </div>
        {marketingSaveArr.map((item) => (
          <div
            className="isoDropdownLink"
            onClick={() => {
              setFilter(item.key);
              setMarketingFilter(item.text);
              setFilterVisible(!filterVisible);
            }}
            role="button"
            tabIndex="-1"
          >
            {item.text}
          </div>
        ))}
      </div>
    </WidgetWrapper>
  );

  const Mapdata = [
    {
      coordinates: [103.7416434, 1.3439162], // Longitude, Latitude (Singapore)
      key: 'singapore',
      country: 'Singapore',
      totalCount: 100,
    },
    {
      coordinates: [-94.6009494, 24.9168638], // Longitude, Latitude (NewYork)
      key: 'newyork',
      country: 'New York',
      totalCount: 150,
    },
    {
      coordinates: [-122.3103585, 37.3616343], // Longitude, Latitude (SanFrancisco)
      key: 'sanfrancisco,',
      country: 'San Francisco,',
      totalCount: 150,
    },
    {
      coordinates: [150.6023339, -33.8472349], // Longitude, Latitude (Sydney)
      key: 'sydney',
      country: 'Sydney',
      totalCount: 150,
    },
    // Add more data as needed
  ];

  const CustomLegend = (props, type) => {
    const { payload } = props;
    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'left',
          paddingTop: 10,
          gap: 40,
        }}
      >
        {payload?.map((entry) => (
          <div
            key={`item-${entry}`}
            style={{ marginRight: 10, display: 'flex', alignItems: 'center' }}
          >
            {console.log(
              entry?.payload?.displayName === 'Product A' ||
                'Product B' ||
                'Product',
              '=========',
            )}
            <div
              style={{
                width: 10,
                height: 10,
                backgroundColor: entry.color,
                marginRight: 5,
                borderRadius: '50%',
              }}
            />
            <span
              style={{
                color:
                  entry?.payload?.displayName === 'Product A' ||
                  'Product B' ||
                  'Product'
                    ? 'rgba(1, 1, 1, 1)'
                    : 'rgb(75, 81, 87)',
                lineHeight: '20px',
                fontFamily: 'Inter',
              }}
            >
              {entry?.payload?.displayName || 'static data'}
            </span>
          </div>
        ))}
      </div>
    );
  };

  /**
   * Function to render Top 4 boxes
   */
  const renderTopBoxes = () => {
    return (
      <Row
        style={{ ...rowStyle, width: '100%', padding: '40px 40px 0px 40px' }}
        gutter={0}
        justify="start"
      >
        {TOP_BOXES.map((widget) => {
          return (
            <Col
              xl={4}
              md={8}
              sm={24}
              xs={22}
              style={{ ...colStyle, marginBottom: 28 }}
              key={widget.text}
            >
              <IsoWidgetsWrapper>
                <LinkBox link="">
                  <StickerWidget
                    number={widget.count}
                    text={<IntlMessages id={widget.text} />}
                    icon={widget.icon}
                    fontColor={widget.fontColor}
                    bgColor={widget.bgColor}
                    textPT="30px"
                    textBottomPT="10px"
                    percentage={widget.percentage}
                    isUp={widget.isUp}
                  />
                </LinkBox>
              </IsoWidgetsWrapper>
            </Col>
          );
        })}
        <Col xl={8} md={16} sm={24} xs={24} style={{ ...colStyle }}>
          <IsoWidgetsWrapper style={{ borderRadius: 10 }} className="osChart">
            <Spin spinning={accLoading}>
              <StickerWidgetWrapper
                className="isoStickerWidget"
                style={{
                  backgroundColor: '#ffffffff',

                  boxShadow: '0px 4px 10px 4px rgba(58, 43, 125, 0.04)',
                  borderRadius: 10,
                  height: 200,
                  display: 'flex',
                  flexDirection: 'column',
                  padding: '15px',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginBottom: 5,
                  }}
                >
                  <h4
                    style={{
                      fontWeight: '500',
                      fontSize: '14px',
                      lineHeight: '20px',
                      color: 'rgba(28, 28, 28, 1)',
                      fontFamily: 'Inter',
                    }}
                  >
                    <IntlMessages id="dashboard.sessionsByLocation" />
                  </h4>
                  <Popover
                    content={renderPopover('trafficByLocation')}
                    trigger="click"
                    visible={durationVisible === 'trafficByLocation'}
                    onVisibleChange={(isVisible) => {
                      durationChange('trafficByLocation', isVisible);
                    }}
                    placement="bottomRight"
                  >
                    <p
                      style={{
                        cursor: 'pointer',
                        fontSize: '10px',
                        background: 'rgba(250, 251, 252, 1)',
                        color: 'rgba(102, 112, 133, 1)',
                        display: 'flex',
                        alignItems: 'center',
                        padding: '8px 10px',
                        borderRadius: 6,
                        fontWeight: 500,
                        fontFamily: 'Inter',
                        lineHeight: '12.1px',
                      }}
                    >
                      {duration.trafficByLocation}
                      <DownOutlined style={{ marginLeft: 5 }} />
                    </p>
                  </Popover>
                </div>
                <Row
                  gutter={10}
                  style={{
                    ...rowStyle,
                    display: 'flex',
                    height: 136,
                    justifyContent: 'space-between',
                  }}
                >
                  <Col sm={16} md={12} xl={13} xs={12} className="map">
                    <MapChart
                      array={dData?.data?.userByRegion ?? Mapdata}
                      setToolTipContent={setToolTipContent}
                    />
                  </Col>
                  <Col sm={8} md={12} xl={11} xs={12}>
                    <ProgressBar array={dData?.data?.userByRegion || []} />
                  </Col>
                </Row>
              </StickerWidgetWrapper>
            </Spin>
          </IsoWidgetsWrapper>
        </Col>
      </Row>
    );
  };

  return (
    <LayoutWrapper>
      <DashboardWrapper>
        <Spin spinning={dData.loading}>
          <div style={styles.wisgetPageStyle}>
            {/* Number box */}
            {renderTopBoxes()}

            {/* Main Dashboard (CUSTOMER OVERVIEW & VISITS BY TYPE) */}
            <Row
              style={{ ...rowStyle, padding: '0px 40px 0px 40px' }}
              gutter={6}
              justify="start"
            >
              {/* CUSTOMER OVERVIEW */}
              <Col lg={12} md={24} sm={24} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  <ReportsWidget
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 10,
                      border: 0,
                      boxShadow: '0px 4px 4px 0px rgba(171, 201, 245, 0.09)',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginBottom: 16,
                      }}
                    >
                      <h4
                        style={{
                          fontWeight: '500',
                          fontSize: '14px',
                          color: 'rgba(38, 45, 51, 1)',
                          lineHeight: '20px',
                          letterSpacing: '0.5px',
                          fontFamily: 'Inter',
                        }}
                      >
                        <IntlMessages id="dashboard.custOverview" />
                      </h4>
                      <Popover
                        content={renderPopover('custumerOverview')}
                        trigger="click"
                        visible={durationVisible === 'custumerOverview'}
                        onVisibleChange={(isVisible) =>
                          durationChange('custumerOverview', isVisible)
                        }
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: 400,
                            lineHeight: '20px',
                            color: 'rgba(75, 81, 87, 1)',
                            fontFamily: 'Inter',
                          }}
                        >
                          {duration.customerOverview}
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                    </div>
                    <Spin spinning={accLoading}>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart
                          width="100%"
                          height="100%"
                          data={dData?.data?.customerOverview}
                          margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                          style={{
                            fontSize: 12,
                            fontWeight: 400,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <Tooltip />
                          <Legend content={<CustomLegend />} />
                          <Bar
                            dataKey="number_of_users"
                            name="Number of Users"
                            displayName="No. Of users"
                            fill="#227FF1"
                            borderRadius={10}
                            radius={[20, 20, 0, 0]}
                          />
                          <Bar
                            dataKey="number_of_visits"
                            name="Total Visits"
                            displayName="Total Visits"
                            fill="#92B7D0"
                            radius={[20, 20, 0, 0]}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </Spin>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              {/* VISITS BY TYPE */}
              <Col lg={12} md={24} sm={24} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  <ReportsWidget
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 10,
                      border: 0,
                      boxShadow: '0px 4px 4px 0px rgba(171, 201, 245, 0.09)',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginBottom: 16,
                      }}
                    >
                      <h4
                        style={{
                          fontWeight: '400',
                          fontSize: '14px',
                          color: 'rgba(38, 45, 51, 1)',
                          lineHeight: '20px',
                          letterSpacing: '0.5px',
                          fontFamily: 'Inter',
                        }}
                      >
                        <IntlMessages id="dashboard.productType" />
                      </h4>
                      <Popover
                        content={renderPopover('sessionProductType')}
                        trigger="click"
                        visible={durationVisible === 'sessionProductType'}
                        onVisibleChange={(isVisible) =>
                          durationChange('sessionProductType', isVisible)
                        }
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: 400,
                            lineHeight: '20px',
                            color: 'rgba(75, 81, 87, 1)',
                            fontFamily: 'Inter',
                          }}
                        >
                          {duration.sessionProductType}
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                    </div>
                    <Spin spinning={accLoading}>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart
                          width="100%"
                          height="100%"
                          data={(
                            dData?.data?.session_product_chart?.finalRes || []
                          ).map((item) => ({
                            month: item.month,
                            ...item.product_types,
                          }))}
                          margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                          style={{
                            fontSize: 12,
                            fontWeight: 400,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend content={<CustomLegend />} />
                          {dData?.data?.session_product_chart?.products?.map(
                            (li, ind) => {
                              return (
                                <Bar
                                  dataKey={li}
                                  width={2}
                                  fill={colors[ind]}
                                  radius={[20, 20, 20, 20]}
                                  displayName={li.toUpperCase()}
                                />
                              );
                            },
                          )}
                        </BarChart>
                      </ResponsiveContainer>
                    </Spin>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
            </Row>

            <Row
              style={{ ...rowStyle, padding: '0px 40px 0px 40px' }}
              gutter={3}
              justify="start"
            >
              <Col lg={15} md={24} sm={24} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  <ReportsWidget
                    label={
                      <span
                        style={{
                          fontSize: '18px',
                          fontWeight: 500,
                          fontFamily: 'Inter',
                          lineHeight: '28px',
                          letterSpacing: '0.5%',
                          color: 'rgba(51, 56, 67, 1)',
                        }}
                      >
                        <IntlMessages id="dashboard.marketingCampaigns" />
                      </span>
                    }
                    label2={
                      <div className="marketTableActions">
                        <div>
                          <DatePicker
                            className="marketingCampaingDatePicker"
                            onChange={(val, dateString) => {
                              setCompaignDate(dateString);
                            }}
                          />
                        </div>
                        <div>
                          <Popover
                            content={marketCampaignFilterContent}
                            trigger="click"
                            visible={filterVisible}
                            onVisibleChange={() => filterChange()}
                            placement="bottomRight"
                          >
                            <p
                              style={{
                                cursor: 'pointer',
                                fontSize: '14px',
                              }}
                            >
                              <Button
                                onClick={filterChange}
                                name="filter"
                                className="marketingCampaingFilter"
                                icon={
                                  <FilterIcon style={{ fontSize: '24px' }} />
                                }
                              >
                                {marketingFilter || 'Filters'}
                              </Button>
                            </p>
                          </Popover>
                        </div>
                      </div>
                    }
                    labelClsName="labelStyle"
                    className="campaignsList"
                    analyticStyle={{
                      justifyContent: 'space-between',
                      color: '#000 !important',
                    }}
                    style={{
                      height: '100%',
                      borderRadius: 8,
                      boxShadow: '0px 1.5px 2px 0px rgba(16, 24, 40, 0.1)',
                      padding: 0,
                      border: '1px solid rgba(245, 245, 245, 1)',
                    }}
                    widgetClassName="flex1"
                    action={
                      <div
                        style={{
                          cursor: 'pointer',
                          textAlign: 'right',
                          position: 'absolute',
                          bottom: '25px',
                          color: '#04B8FF',
                          textDecoration: 'underline',
                          fontSize: '14px',
                          fontWeight: 600,
                          fontFamily: 'Inter',
                        }}
                        onClick={() =>
                          history.push({
                            pathname: '/dashboard/marketing-new',
                          })
                        }
                      >
                        <IntlMessages id="action.viewcampaign" />
                      </div>
                    }
                  >
                    <TableWrapper
                      loading={false}
                      rowKey={(record) => record.id}
                      dataSource={dData?.data?.marketingCampaigns}
                      // onChange={handleTableChange}
                      columns={marketingCampaignCol}
                      pagination={{ showSizeChanger: false }}
                      className="invoiceListTable"
                      showSorterTooltip={false}
                      style={{
                        height: '100%',
                        overflow: 'auto',
                        borderRadius: '8px', // Add your border radius here
                      }}
                    />
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              <Col lg={9} md={24} sm={24} xs={24} style={colStyle}>
                <IsoWidgetsWrapper
                  style={{
                    height: '100%',
                  }}
                >
                  <ReportsWidget
                    style={{
                      height: '100%',
                      borderRadius: 8,
                      boxShadow: '0px 1.5px 2px 0px rgba(16, 24, 40, 0.1)',
                      padding: 0,
                      border: '1px solid rgba(240, 241, 243, 1)',
                    }}
                    labelClsName="labelStyle"
                    className="campaignsList"
                    analyticStyle={{
                      justifyContent: 'space-between',
                      color: '#000 !important',
                      width: '100%',
                      paddingTop: 12,
                    }}
                    label={
                      <div
                        style={{
                          fontSize: '18px',
                          fontWeight: 500,
                          fontFamily: 'Inter',
                          lineHeight: '28px',
                          letterSpacing: '0.5%',
                          color: 'rgba(51, 56, 67, 1)',
                        }}
                      >
                        <IntlMessages id="sidebar.service" />
                      </div>
                    }
                    label2={
                      <div className="marketTableActions">
                        <div
                          style={{
                            cursor: 'pointer',
                            color: 'rgba(4, 184, 255, 1)',
                            textDecoration: 'underline',
                            fontSize: '14px',
                            fontWeight: 600,
                            fontFamily: 'Inter',
                            lineHeight: '20px',
                          }}
                          onClick={() =>
                            history.push({
                              pathname: '/dashboard/support',
                            })
                          }
                        >
                          <IntlMessages id="dashboard.viewAllTickets" />
                        </div>
                      </div>
                    }
                    widgetClassName="flex1"
                  >
                    <TableWrapper
                      loading={false}
                      rowKey={(record) => record.id}
                      dataSource={dData?.data?.chatTickets}
                      // onChange={handleTableChange}
                      columns={customerCareCol}
                      pagination={false}
                      className="invoiceListTable"
                      showSorterTooltip={false}
                      style={{
                        height: '100%',
                        overflow: 'auto',
                        borderRadius: '8px',
                      }}
                    />
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
            </Row>
          </div>
        </Spin>
      </DashboardWrapper>
    </LayoutWrapper>
  );
}

export default injectIntl(Widgets);
