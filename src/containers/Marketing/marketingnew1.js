/* eslint-disable array-callback-return */
/* eslint-disable no-param-reassign */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable  react/jsx-no-bind */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-else-return */

import React, { useEffect, useState } from 'react';

import {
  Col,
  Row,
  Card,
  Avatar,
  Dropdown,
  Space,
  Divider,
  Switch,
  Menu,
  Slider,
  Button,
  Progress,
  Table,
  Spin,
  Pagination,
  Typography,
  InputNumber,
  Checkbox,
  Input,
} from 'antd';
import { Link, useHistory } from 'react-router-dom';
import {
  StarFilled,
  EllipsisOutlined,
  MenuOutlined,
  AppstoreFilled,
  SettingFilled,
  DownOutlined,
  CopyOutlined,
  RiseOutlined,
  FallOutlined,
  Clock<PERSON>ircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RightOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useSelector } from 'react-redux';
import ContentLoader from 'react-content-loader';
import getApiData from '@chill/lib/helpers/apiHelper';
import { isArray, isEmpty } from 'lodash';
import { AiOutlineCheckCircle } from 'react-icons/ai';
import theme from '@chill/config/theme/default';
import gallery from '@chill/assets/images/gallery.svg';
import IntlMessages from '@chill/components/utility/intlMessages';
import expandIcon from '@chill/assets/images/up-arrow.png';
import filterDrawerIcon from '@chill/assets/images/filter-drawer-icon.png';
import { Line, LineChart, ResponsiveContainer, Tooltip } from 'recharts';
import downArrow from '@chill/assets/images/down-arrow.png';
import overViewBackImg2 from '@chill/assets/images/ai-marketing-overview.png';
import overViewBackImg from '@chill/assets/images/ai-back.png';
import PageTitleHeader from '@chill/components/uielements/PageTitleHeader';
import styled from 'styled-components';
import MarketingNew1Wrapper from './MarketingNew1Wrapper.style';

const { Text } = Typography;

export default function MarketingNew() {
  // Array of menus for Dropdowm
  const history = useHistory();
  const items = [
    {
      label: 'Value',
      key: '0',
      value: 'value',
    },
    {
      label: 'Audience size',
      key: '1',
      value: 'audienceSize',
    },
    {
      label: 'Conversion rate',
      key: '3',
      value: 'conversionRate',
    },
    {
      label: 'Quantity',
      key: '4',
      value: 'quantity',
    },
  ];
  // store the state of selected icon
  const [selectedIcon, setSelectedIcon] = useState('AppstoreFilled');

  // const [selectedDropdownItem, setSelectedDropdownItem] = useState(items[0]);

  // store the state of sliderRange
  const [sliderRange, setSliderRange] = useState([6, 9]);
  const onAfterChange1 = (value) => {
    console.log('onAfterChange: ', value);
    setSliderRange(value);
  };

  const onChange1 = (value) => {
    console.log('onChange: ', value);
    setSliderRange(value);
  };
  // store the state of selected options and keys
  const { SubMenu } = Menu;
  const [openKeys, setOpenKeys] = useState();
  const [selectedOptions, setSelectedOptions] = useState({});
  console.log(
    `hello ~ file: marketingnew1.js:118 ~ selectedOptions:`,
    selectedOptions,
  );
  const [aiBoxData, setAiBoxData] = useState({});

  const [cards, setCards] = useState([]);
  const [filteredCards, setFilteredCards] = useState(cards);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilter, setShowFilter] = useState(true);
  const [shopifyProductState, setShopifyProductState] = React.useState({
    products: [],
    productLoad: false,
    productData: {},
  });
  const [minAudSliderValue, setMinAudSliderValue] = useState(0);
  const [maxAudSliderValue, setMaxAudSliderValue] = useState(1000);
  const [minQtySliderValue, setMinQtySliderValue] = useState(0);
  const [maxQtySliderValue, setMaxQtySliderValue] = useState(1000);
  const [audienceRange, setAudienceRange] = useState([100, 500]);
  const [filterPriceRange, setFilterPriceRange] = useState([0, 1000]);

  const [qtyRange, setqtyRange] = useState([100, 500]);
  const [emailChecked, setEmailChecked] = useState(false);

  // const [shopifyFilter, setshopifyFilter] = React.useState({
  //   pagination: { simple: true },
  //   filters: {},
  // });
  // const onOpenChange = (keys) => {
  //   setOpenKeys(keys);
  // };
  // function for selecting menu items
  // const handleOptionSelect = (event, key) => {
  //   setSelectedOptions({ ...selectedOptions, [key]: event.target.checked });
  // };

  // const handleApplyClick = () => {
  //   localStorage.setItem('selectedOptions', JSON.stringify(selectedOptions));
  // };
  // function for checkbox clicked
  const onChange = (checked) => {
    setEmailChecked(checked);
    console.log(`switch to ${checked}`);
  };

  // functions for handling drag and drop
  function handleDragEnd(result) {
    if (!result.destination) return;

    const { source, destination } = result;

    if (source.index === destination.index) return;

    const newCards = [...cards];
    const [draggedCard] = newCards.splice(source.index, 1);

    newCards.splice(destination.index, 0, draggedCard);

    setCards(newCards);
  }

  // this function for get shopify products
  /** this function for get shopify products
   * @function getShopifyProductList
   * @param {object} data type, sort
   */

  async function getShopifyProductList(data = {}) {
    // call this api

    setShopifyProductState((p) => ({
      ...p,
      productLoad: true,
    }));

    // if (data.product_tag || data.sort_obj) {
    data.collection_id = 1; // selectedCollectionId;

    try {
      const response = await getApiData('product/get-brand-products-ai');
      if (response.success) {
        console.log('response', response, 'response');
        setShopifyProductState((prevState) => ({
          ...prevState,
          products: isArray(response?.data) ? response?.data : [],
          productLoad: false,
          productData: response,
        }));
      }
    } catch (err) {
      console.log('error generating the campaign');
    }
  }

  async function getAiBoxData(data = {}) {
    try {
      const response = await getApiData(
        'product/get-ai-dashboard-data',
        data,
        'GET',
      );
      if (response.success) {
        setAiBoxData(response.data);
        console.log('response', response, 'response');
      }
    } catch (err) {
      console.log('error generating the campaign');
    }
  }

  React.useEffect(() => {
    getShopifyProductList();
    getAiBoxData();
  }, []);

  useEffect(() => {
    console.log('shofiyyyyy prrrr', shopifyProductState.products);
    if (shopifyProductState.products?.length > 0) {
      const firstSixElements = shopifyProductState?.products;
      setCards(firstSixElements);
      setFilteredCards(firstSixElements);
      const lowestAudCount = Math.min(
        ...firstSixElements.map((data) => data.audience_count),
      );
      const highestAudCount = Math.max(
        ...firstSixElements.map((data) => data.audience_count),
      );

      setMinAudSliderValue(Math.floor(lowestAudCount / 10) * 10);
      setMaxAudSliderValue(Math.ceil(highestAudCount / 10) * 10);
      setAudienceRange([
        Math.floor(lowestAudCount / 10) * 10,
        Math.ceil(highestAudCount / 10) * 10,
      ]);
      const lowestQtyCount = Math.min(
        ...firstSixElements.map((data) => data.estimated_sell),
      );
      const highestQtyCount = Math.max(
        ...firstSixElements.map((data) => data.estimated_sell),
      );

      setMinQtySliderValue(Math.floor(lowestQtyCount / 10) * 10);
      setMaxQtySliderValue(Math.ceil(highestQtyCount / 10) * 10);
      setqtyRange([
        Math.floor(lowestQtyCount / 10) * 10,
        Math.ceil(highestQtyCount / 10) * 10,
      ]);
    }
  }, [shopifyProductState]);

  // const [selectedQuantity, setSelectedQuantity] = useState('');
  // const [selectedAudienceSize, setSelectedAudienceSize] = useState('');

  // // Function to handle the quantity filter selection
  // const handleQuantityFilter = (option) => {
  //   setSelectedQuantity(option);
  // };

  // const handleAudienceFilter = (option) => {
  //   setSelectedAudienceSize(option);
  // };

  // // Function to apply filters and update the cards state with the filtered results
  // const handleApplyClick = () => {
  //   // Filter the cards based on selected quantity
  //   const filtered = cards.filter((card) => {
  //     const matchQuantity =
  //       !selectedQuantity ||
  //       (selectedQuantity === '0-100' &&
  //         card.estimated_sell >= 0 &&
  //         card.estimated_sell <= 100) ||
  //       (selectedQuantity === '100-150' &&
  //         card.estimated_sell >= 100 &&
  //         card.estimated_sell <= 150) ||
  //       (selectedQuantity === '150-200' &&
  //         card.estimated_sell >= 150 &&
  //         card.estimated_sell <= 200);

  //     const matchAudienceSize =
  //       !selectedAudienceSize ||
  //       (selectedAudienceSize === '0-500' &&
  //         card.audience_count >= 0 &&
  //         card.audience_count <= 500) ||
  //       (selectedAudienceSize === '>500' && card.audience_count > 500);

  //     return matchQuantity && matchAudienceSize;
  //   });

  //   setFilteredCards(filtered);
  // };

  // useEffect(() => {

  // }, []);

  const applyFilter = () => {
    const [minAudience, maxAudience] = audienceRange;
    const [minPrice, maxPrice] = filterPriceRange;

    const filteredData = cards?.filter((data) => {
      const { audience_count, estimated_sell, sell_price } = data;
      return (
        audience_count >= minAudience &&
        audience_count <= maxAudience &&
        estimated_sell <= (selectedOptions?.qty81 ? 81 : 0) &&
        estimated_sell <= (selectedOptions?.qty95 ? 95 : Infinity) &&
        sell_price >= minPrice &&
        sell_price <= maxPrice
      );
    });

    setFilteredCards(filteredData);
  };

  // store the state of sliderRange

  const onAudienceChange = (value) => {
    console.log('onChange: ', value);
    setAudienceRange(value);
  };
  const onqtyChange = (value) => {
    console.log('onChange: ', value);
    setqtyRange(value);
  };

  const onOpenChange = (keys) => {
    setOpenKeys(keys);
  };
  // function for selecting menu items
  const handleOptionSelect = (event, key) => {
    setSelectedOptions({ ...selectedOptions, [key]: event.target.checked });
  };

  const cardsPerPage = 8;

  const startIndex = (currentPage - 1) * cardsPerPage;
  const endIndex = startIndex + cardsPerPage;
  const cardsToDisplay = filteredCards.slice(startIndex, endIndex);
  console.log(
    `hello ~ file: marketingnew1.js:322 ~ getShopifyProductList ~ cardsToDisplay:`,
    cardsToDisplay,
  );

  // functions for showing the total no of campaigns and total value of campaigns

  const totalCamp = shopifyProductState.products.length;

  const totalCampaignValue = shopifyProductState.products.reduce(
    (total, campaign) => {
      return total + campaign.campaign_value;
    },
    0,
  );

  const formattedTotalCampaignValue = totalCampaignValue.toLocaleString();

  const campValBoxes = [
    {
      key: 'activePercentage',
      text: 'marketing.totalValue',
      fontColor: '#04B8FF',
      bgColor: '#ffffffff',
      boxObj: aiBoxData?.totalValueDetails,
      count: `$${formattedTotalCampaignValue}`, // !isEmpty(dData.datad) ? ${dData.datad?.activePercentage}% : 0,
      img: (
        <AiOutlineCheckCircle
          style={{
            width: 5,
            height: 5,
            color: theme.colors.primaryColor,
            marginLeft: 0,
          }}
        />
      ),
    },
    {
      key: 'bgPercentage',
      text: 'marketing.NumbCampaigns',
      fontColor: '#00D9C0',
      bgColor: '#ffffffff',
      boxObj: aiBoxData?.totalCampaignDetails,
      count: totalCamp, // !isEmpty(dData.datad) ? ${dData.datad?.bgPercentage}% : 0,
      img: <img src={gallery} alt="" />,
    },
  ];

  function renderCampBoxes() {
    return (
      <Row className="boxRowStyle" gutter={[22, 17]}>
        {campValBoxes.map((widget) => {
          const rawData = [
            widget?.boxObj?.currentWeekCnt > 0
              ? widget?.boxObj?.currentWeekCnt
              : null,
            widget?.boxObj?.lastWeekCnt > 0
              ? widget?.boxObj?.lastWeekCnt
              : null,
          ];
          // const rawData = [10, 25, 0, 80];
          const checkNullVal =
            rawData?.map((value, i) =>
              value !== null ? { i, value } : null,
            ) || [];
          const lineChartArr = checkNullVal?.some((v) => v === null)
            ? []
            : checkNullVal;
          return (
            <Col xs={24} className="innerBoxCol">
              <div className="boxesStyle">
                <div className="leftBoxDiv">
                  <div className="innerLeftDiv">
                    <span className="label">
                      <IntlMessages id={widget.text} />
                    </span>
                    <span className="label-val">
                      {widget?.key === 'activePercentage' && '$'}
                      {widget?.boxObj?.currentWeekCnt.toLocaleString() || 0}
                    </span>
                  </div>
                </div>
                <div className="rightBoxDiv">
                  <div className="innerRightDiv">
                    <span
                      className="label"
                      style={{
                        color: widget.fontColor,
                      }}
                    >
                      {widget?.boxObj?.cntDifferencePercentage}%
                    </span>
                    {!isEmpty(lineChartArr) && (
                      <div style={{ height: 100, width: 100 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart
                            width={500}
                            height={300}
                            data={lineChartArr}
                            margin={{
                              top: 5,
                              right: 30,
                              left: 20,
                              bottom: 5,
                            }}
                          >
                            {/* <CartesianGrid strokeDasharray="3 3" /> */}
                            {/* <XAxis dataKey="name" />
                    <YAxis /> */}
                            <Tooltip />
                            <defs>
                              {/* Define a filter for box-shadow */}
                              <filter
                                id="shadow"
                                x="-50%"
                                y="-50%"
                                width="200%"
                                height="200%"
                              >
                                <feDropShadow
                                  dx="0"
                                  dy="4"
                                  stdDeviation="2"
                                  floodColor="#21965329"
                                />
                              </filter>
                            </defs>
                            {/* <Legend /> */}
                            <Line
                              type="monotone"
                              dataKey="value"
                              strokeWidth={3}
                              style={{ filter: 'url(#shadow)' }}
                              stroke={widget.fontColor}
                              dot={(p) =>
                                p.index === lineChartArr?.length - 1 ? (
                                  <circle {...p} r={5} />
                                ) : null
                              }
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    )}
                    <div />
                  </div>
                </div>
              </div>
            </Col>
          );
        })}
      </Row>
    );
  }

  const InputPriceWrapper = styled.div(() => ({
    '& .price-range-input-div': {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      flexWrap: 'wrap',
      '& .price-range-input': {
        // margin: '0 16px',
        width: '142px',
        height: '50px',
        border: '1px solid #8F8080',
        borderRadius: '5px',
        backgroundColor: '#F9F9F9',
        '& .ant-input-number-input-wrap': {
          height: '100%',
          input: {
            height: '100%',
            fontFamily: 'Inter',
            fontSize: 14,
            fontWeight: 400,
            lineHeight: '28px',
            letterSpacing: '0.20000000298023224px',
            textAlign: 'left',
            textUnderlinePosition: 'from-font',
            textDecorationSkipInk: 'none',
            color: '#737373',
          },
        },
      },
    },
  }));

  const handleMinChange = (value) => {
    // Ensure the value stays within range
    setFilterPriceRange([
      Math.min(value || 0, filterPriceRange[1]),
      filterPriceRange[1],
    ]);
  };

  const handleMaxChange = (value) => {
    // Ensure the value stays within range
    setFilterPriceRange([
      filterPriceRange[0],
      Math.max(value || 0, filterPriceRange[0]),
    ]);
  };

  return (
    <MarketingNew1Wrapper>
      <Row
        style={{
          width: '100%',
        }}
      >
        <Col
          xs={24}
          md={24}
          sm={24}
          xl={showFilter ? 18 : 24}
          xxl={showFilter ? 18 : 24}
          lg={24}
        >
          <Row
            style={{
              padding: showFilter ? '20px 9px 20px 40px' : '20px 40px',
            }}
            className="ai-marketing-left"
          >
            <Col xs={24} className="header-marketing">
              <PageTitleHeader
                title={<IntlMessages id="title.AIMarketing" />}
              />

              <div className="header-btns">
                {/* <div>
                <AppstoreFilled
                  style={{
                    width: '24px',
                    height: '24px',
                    fontSize: '25px',
                    color:
                      selectedIcon === 'AppstoreFilled' ? '#2A77F4' : '#A9ABBC',
                    marginLeft: '23px',
                    marginTop: '28px',
                    cursor: 'pointer',
                  }}
                  onClick={() => setSelectedIcon('AppstoreFilled')}
                />
                <MenuOutlined
                  style={{
                    width: '24px',
                    fontSize: '23px',
                    height: '24px',
                    color:
                      selectedIcon === 'MenuOutlined' ? '#2A77F4' : '#A9ABBC',
                    marginLeft: '25px',
                    marginTop: '28px',
                    cursor: 'pointer',
                  }}
                  onClick={() => setSelectedIcon('MenuOutlined')}
                />
              </div> */}
                {!showFilter && (
                  <Button
                    type="text"
                    style={{
                      padding: '0px',
                    }}
                    onClick={() => setShowFilter((prevState) => !prevState)}
                  >
                    <img
                      src={filterDrawerIcon}
                      alt="filterDrawerIcon"
                      // style={{ width: '24px', height: '24px' }}
                    />
                  </Button>
                )}
              </div>
            </Col>
            <Col xs={24} className="box-withoverview">
              <Row
                style={{
                  width: '100%',
                }}
              >
                <Col xs={24} sm={24} md={12} lg={9} xl={9} xxl={9}>
                  {renderCampBoxes()}
                </Col>
                <Col xs={24} sm={24} md={12} lg={15} xl={15} xxl={15}>
                  <div
                    className="overViewCol"
                    style={{
                      background: `url(${overViewBackImg})`,
                      height: '187px',
                      backgroundSize: 'cover',
                    }}
                  >
                    <div
                      className="overViewContainer"
                      style={{
                        height: '100%',
                        backgroundSize: 'cover',
                        backdropFilter: 'blur(25px)',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        boxShadow:
                          '0px 1px 40px 0px #0D89CF33 inset, 0px 4px 18px 0px #083B584D inset, 0px 98px 100px -48px #00A1FD4D inset, 0px -82px 68px -64px #0E4E724D inset, 0px 7px 11px -4px #FFFFFF inset, 0px 39px 56px -36px #FFFFFF80 inset',
                      }}
                    >
                      <div
                        style={{
                          maxWidth: '345px',
                        }}
                      >
                        <Text className="overViewText">
                          Our AI campaign generator is recommending the
                          following campaigns to the audiences included. Our AI
                          has determined that these customers are in need of
                          these products or more likely to buy at this time.
                        </Text>
                      </div>
                    </div>
                  </div>
                  {/* <h4
                    style={{
                      color: '#fff',
                    }}
                  >
                    OVERVIEW
                  </h4>

                 */}
                </Col>
              </Row>
            </Col>

            <Col xs={24}>
              <div
                style={{
                  height: '100%',
                  width: '100%',
                  paddingTop: '29.86px',
                  paddingLeft: '0px',
                  paddingRight: '0px',
                  paddingBottom: '10px',

                  overflowX: 'hidden',
                  overflowY: 'hidden',
                  whitespace: 'nowrap',
                  display: selectedIcon === 'AppstoreFilled' ? 'block' : 'none',
                }}
              >
                <Spin spinning={shopifyProductState.productLoad}>
                  <DragDropContext onDragEnd={handleDragEnd}>
                    <Droppable droppableId="cards">
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                        >
                          <Row gutter={[24, 19]}>
                            {cardsToDisplay.map((card, index) => (
                              <Draggable
                                key={card.id}
                                draggableId={card.id.toString()}
                                index={index}
                              >
                                {(dragProvided) => (
                                  <Col
                                    span={12}
                                    md={6}
                                    ref={dragProvided.innerRef}
                                    {...dragProvided.draggableProps}
                                    {...dragProvided.dragHandleProps}
                                  >
                                    <Card
                                      style={{
                                        width: '242px',
                                        // height: '44vh',
                                        borderRadius: '10px',
                                      }}
                                      bodyStyle={{
                                        padding: '6px 14px 14px 21px',
                                      }}
                                      hoverable
                                      cover={
                                        <img
                                          src={card?.product_image}
                                          style={{
                                            width: '100%',
                                            height: '81px',
                                            backgroundColor: '#eee',
                                            objectFit: 'cover',
                                            borderTopLeftRadius: '10px',
                                            borderTopRightRadius: '10px',
                                          }}
                                          alt=""
                                        />
                                      }
                                      onClick={() => {
                                        history.push({
                                          pathname: '/dashboard/card',
                                          state: {
                                            id: card.id,
                                            image: card?.product_image,
                                            name: card.product_name,
                                            price: card.sell_price,
                                            qty: card.estimated_sell,
                                            audi: card.audience_count,
                                            camp: card.campaign_value,
                                          }, // your data array of objects
                                        });
                                      }}
                                    >
                                      <div style={{}}>
                                        <div
                                          style={{
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                          }}
                                        >
                                          <p
                                            style={{
                                              color: '#404E68',
                                              fontFamily: 'Inter',
                                              fontSize: '13px',
                                              fontWeight: 700,
                                              lineHeight: '15.73px',
                                              textAlign: 'left',
                                              textUnderlinePosition:
                                                'from-font',
                                              textDecorationSkipInk: 'none',
                                            }}
                                          >
                                            {card.product_name}
                                          </p>
                                          <div />
                                          <p
                                            style={{
                                              fontFamily: 'Inter',
                                              fontSize: '12px',
                                              fontWeight: 700,
                                              lineHeight: '22px',
                                              textAlign: 'left',
                                              textUnderlinePosition:
                                                'from-font',
                                              textDecorationSkipInk: 'none',
                                              color: '#8E95A9',
                                            }}
                                          >
                                            ${card?.sell_price}
                                          </p>
                                        </div>
                                        <div
                                          className="cardDetails"
                                          style={{
                                            marginTop: '6px',
                                          }}
                                        >
                                          <span className="cardLabel">
                                            Sale Estimate
                                          </span>
                                          <span className="cardValue">
                                            {card.estimated_sell}
                                          </span>
                                        </div>
                                        <div className="cardDetails">
                                          <span className="cardLabel">
                                            Audience Count
                                          </span>
                                          <span className="cardValue">
                                            {card.audience_count}
                                          </span>
                                        </div>
                                        <div className="cardDetails">
                                          <span className="cardLabel">
                                            Campaign Value
                                          </span>
                                          <span className="cardValue">
                                            $
                                            {card.campaign_value.toLocaleString()}
                                          </span>
                                        </div>
                                        <div
                                          style={{
                                            display: 'none',
                                            justifyContent: 'flex-end',
                                          }}
                                        >
                                          <div
                                            style={{
                                              fontSize: '16px',
                                              color: '#FFC107',
                                              marginLeft: '105px',
                                              marginTop: '5px',
                                            }}
                                          >
                                            <StarFilled />
                                          </div>
                                          <div
                                            style={{
                                              fontSize: '20px',
                                              color: '#A9ABBC',
                                              marginLeft: '5px',
                                              marginTop: '5px',
                                            }}
                                          >
                                            <EllipsisOutlined />
                                          </div>
                                        </div>
                                      </div>
                                    </Card>
                                    {/* </Link> */}
                                  </Col>
                                )}
                              </Draggable>
                            ))}

                            {/* <Col span={12} md={6}>
                            <Card
                              hoverable
                              style={{
                                width: '14vw',
                                // height: '28vh',
                                borderRadius: '12px',
                                border: '6px solid #7070701A',
                              }}
                            >
                              <CopyOutlined
                                style={{
                                  flex: 1,
                                  fontSize: '40px',
                                  color: '#A9ABBC',
                                  marginTop: '60px',
                                  marginLeft: '40px',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                }}
                              />
                              <h5
                                style={{
                                  color: 'A9ABBC',
                                  fontFamily: 'Rubik',
                                  fontSize: '12px',
                                  textAlign: 'center',
                                }}
                              >
                                Make a copy
                              </h5>
                              <div style={{ height: 80 }} />
                            </Card>
                          </Col> */}
                          </Row>

                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </DragDropContext>
                </Spin>
              </div>

              <div
                style={{
                  height: '100%',
                  width: '58.5vw',
                  paddingLeft: showFilter ? '23px' : '70px',
                  // paddingRight: '23px',

                  overflowY: 'overflow',
                  whitespace: 'nowrap',
                  marginTop: '20px',
                  paddingBottom: '10px',
                  display: selectedIcon === 'MenuOutlined' ? 'block' : 'none',
                }}
              >
                {cardsToDisplay.map((card, index) => (
                  <Card
                    hoverable
                    style={{
                      width: showFilter ? '56vw' : '65vw',
                      height: showFilter ? '17vh' : '17vh',
                      marginLeft: '-10px',
                      borderRadius: '12px',
                      marginTop: '20px',
                    }}
                    onClick={() => {
                      history.push({
                        pathname: '/dashboard/card',
                        state: {
                          id: card.id,
                          image: card?.product_image,
                          name: card.product_name,
                          price: card.sell_price,
                          qty: card.estimated_sell,
                          audi: card.audience_count,
                          camp: card.campaign_value,
                        }, // your data array of objects
                      });
                    }}
                  >
                    <div style={{ display: 'flex', flexDirection: 'row' }}>
                      <img
                        src={card?.product_image}
                        alt="mydata"
                        style={{
                          width: '11.8vw',
                          height: showFilter ? '17vh' : '17vh',
                          margin: '-25px',
                          borderTopLeftRadius: '12px',
                          borderBottomLeftRadius: '12px',
                        }}
                      />
                      <div style={{ marginLeft: '30px', marginTop: '-40px' }}>
                        <h3
                          style={{
                            color: '#2A77F4',
                            fontSize: '14px',
                            fontFamily: 'Rubik',
                            letterSpacing: '0px',
                            marginTop: '24px',
                            marginLeft: '10px',
                          }}
                        >
                          {card.product_name}
                        </h3>
                        <h5
                          style={{
                            color: '#0A0606',
                            fontSize: '12px',
                            fontFamily: 'Rubik',
                            marginLeft: '10px',
                          }}
                        >
                          ${card.sell_price}
                          {/* ${getPrice(card?.variants)} */}
                        </h5>
                        <p
                          style={{
                            color: '#A9ABBC',
                            fontFamily: 'Rubik',
                            fontSize: '12px',
                            marginLeft: '10px',
                            marginRight: '-30px',
                          }}
                        >
                          Estimated Sold QTY :
                          <span style={{ color: '#0A0606' }}>
                            {card.estimated_sell}
                          </span>
                        </p>
                        <div>
                          <p
                            style={{
                              color: '#A9ABBC',
                              fontFamily: 'Rubik',
                              fontSize: '12px',
                              marginTop: '-40px',
                              marginRight: '-30px',

                              marginLeft: '200px',
                            }}
                          >
                            People in this audience:
                            <span style={{ color: '#0A0606' }}>
                              {card.audience_count}
                            </span>
                          </p>
                          <p
                            style={{
                              color: '#A9ABBC',
                              fontFamily: 'Rubik',
                              fontSize: '12px',
                              marginTop: '5px',
                              marginRight: '-30px',

                              marginLeft: '200px',
                            }}
                          >
                            Value of the campaign:
                            <span style={{ color: '#0A0606' }}>
                              ${card.campaign_value.toLocaleString()}
                            </span>
                          </p>
                        </div>
                      </div>
                      <div style={{ marginTop: '-15px', marginRight: '-15px' }}>
                        <EllipsisOutlined
                          style={{
                            color: '#A9ABBC',
                            fontSize: '30px',
                            marginTop: '-10px',
                            marginRight: '-10px',
                            marginLeft: '200px',
                          }}
                        />
                        <div style={{ display: 'flex' }}>
                          <StarFilled
                            style={{
                              color: '#FFC107',
                              marginLeft: '100px',
                              marginTop: '20px',
                              fontSize: '20px',
                            }}
                          />
                          <StarFilled
                            style={{
                              color: '#FFC107',
                              marginLeft: '15px',
                              marginTop: '20px',
                              fontSize: '20px',
                            }}
                          />
                          <StarFilled
                            style={{
                              color: '#FFC107',
                              marginLeft: '15px',
                              marginTop: '20px',
                              fontSize: '20px',
                            }}
                          />
                          <StarFilled
                            style={{
                              color: '#FFC107',
                              marginLeft: '15px',
                              marginTop: '20px',
                              fontSize: '20px',
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </Col>
            <Col
              xs={24}
              style={{
                // marginTop: '20px',
                display: 'flex',
                justifyContent: 'flex-end',
                padding: '20px',
              }}
            >
              <Pagination
                current={currentPage}
                total={cards.length}
                pageSize={cardsPerPage}
                className="tabel-custom-pagination"
                onChange={(page) => setCurrentPage(page)}
              />
            </Col>
          </Row>
        </Col>
        <Col
          xs={showFilter ? 24 : 0}
          md={showFilter ? 24 : 0}
          sm={showFilter ? 24 : 0}
          xl={showFilter ? 6 : 0}
          xxl={showFilter ? 6 : 0}
          lg={showFilter ? 24 : 0}
        >
          <div
            style={{
              display: showFilter ? 'block' : 'none',
              opacity: showFilter ? 1 : 0,
              transition: 'all 0.5s ease-in-out',
            }}
          >
            <div
              style={{
                width: '100%',
                marginLeft: '15px',
                background: '#fff',
                height: '100%',
                marginRight: '10px',
                padding: '25px 13px 25px 15px',
              }}
            >
              <div className="right-title-content">
                <span className="title">
                  <IntlMessages id="common.filter" />
                </span>
                <Button
                  type="text"
                  onClick={() => setShowFilter((prevState) => !prevState)}
                >
                  <img
                    src={filterDrawerIcon}
                    alt="filterDrawerIcon"
                    // style={{ width: '24px', height: '24px' }}
                  />
                </Button>
              </div>
              {/* <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <h6
                style={{
                  marginTop: '20px',

                  fontFamily: 'Rubik',
                  color: '#A9ABBC',
                }}
              >
                Hello.
              </h6>
              <h4
                style={{
                  marginTop: '4px',

                  fontFamily: 'Rubik',
                  fontSize: '14px',
                  color: '0A0606',
                }}
              >
                Alexander
              </h4>
            </div>
            <div style={{ display: 'flex', marginTop: '20px' }}>
              <Avatar
                src={
                  <img
                    src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                    alt="User"
                  />
                }
              />
              <SettingFilled
                style={{
                  color: '#2A77F4',
                  marginLeft: '5px',
                  marginTop: '7px',
                  marginRight: '5px',
                }}
              />
            </div>
          </div> */}

              <div
                style={{
                  marginTop: '21px',
                  padding: '10px 24px',
                }}
              >
                {/* <h1
              style={{
                marginTop: '-10px',
                fontFamily: 'Rubik',
                fontSize: '14px',
                color: '0A0606',
              }}
            >
              Filter
            </h1> */}
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    marginBottom: '36px',
                  }}
                >
                  <h1
                    style={{
                      fontFamily: 'Inter',
                      fontSize: '14px',
                      fontWeight: 700,
                      lineHeight: '24px',
                      letterSpacing: '0.20000000298023224px',
                      textAlign: 'left',
                      textUnderlinePosition: 'from-font',
                      textDecorationSkipInk: 'none',
                      color: '#252B42',
                    }}
                  >
                    <IntlMessages id="common.emailNotification" />
                  </h1>
                  <Switch
                    checked={emailChecked}
                    defaultChecked
                    onChange={onChange}
                    style={{
                      backgroundColor: emailChecked ? '#04B8FF' : '#718096',
                    }}
                  />
                </div>

                <Menu
                  mode="inline"
                  openKeys={openKeys}
                  onOpenChange={onOpenChange}
                  style={{
                    width: '100%',
                    border: 'none !important',
                  }}
                  className="custom-menu"
                >
                  {/* <SubMenu
                    className="custom-submenu"
                    key="sub1"
                    title="Audience size"
                    expandIcon={({ isOpen }) =>
                      isOpen ? (
                        <img
                          src={downArrow}
                          alt="collapse arrow"
                          // style={{ width: '16px', height: '16px' }}
                        />
                      ) : (
                        <img
                          src={expandIcon}
                          alt="expand arrow"
                          // style={{ width: '16px', height: '16px' }}
                        />
                      )
                    }
                  >
                    <Slider
                      style={{ marginBottom: '10px' }}
                      trackStyle={{ backgroundColor: '#2A77F4' }}
                      range
                      step={1}
                      min={minAudSliderValue}
                      max={maxAudSliderValue}
                      value={audienceRange}
                      onChange={onAudienceChange}
                    />
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'left',
                        color: '#040606',
                        opacity: 0.5,
                        marginTop: 20,
                      }}
                    >
                      <span style={{ fontSize: 10 }}>{audienceRange[0]}</span>
                      <span>-</span>
                      <span style={{ fontSize: 10 }}>{audienceRange[1]}</span>
                    </div>
                  </SubMenu>
                  <Divider
                    style={{
                      margin: '17px 0px',
                      border: '0.7px solid #E9EDF0',
                    }}
                  /> */}

                  {/* <SubMenu
                    hoverable={false}
                    className="custom-submenu"
                    key="sub2"
                    title="Conversion rate"
                    expandIcon={({ isOpen }) =>
                      isOpen ? (
                        <img
                          src={downArrow}
                          alt="collapse arrow"
                          // style={{ width: '16px', height: '16px' }}
                        />
                      ) : (
                        <img
                          src={expandIcon}
                          alt="expand arrow"
                          // style={{ width: '16px', height: '16px' }}
                        />
                      )
                    }
                  >
                    <Menu.Item
                      key="5"
                      style={{
                        // marginLeft: '-20px',
                        padding: '0px',
                        paddingLeft: '0px !important',
                        color: '#A9ABBC',
                        backgroundColor: 'transparent',
                      }}
                    >
                      <input
                        type="checkbox"
                        onChange={(e) => handleOptionSelect(e, '5%')}
                        style={{ marginRight: '10px' }}
                      />
                      18%
                    </Menu.Item>
                  </SubMenu> */}
                  {/* <Divider
                    style={{
                      margin: '36px 0px 17px',
                      border: '0.7px solid #E9EDF0',
                    }}
                  /> */}

                  <SubMenu
                    key="sub3"
                    title="QTY"
                    className="custom-submenu"
                    expandIcon={({ isOpen }) =>
                      isOpen ? (
                        <img
                          src={downArrow}
                          alt="collapse arrow"
                          // style={{ width: '16px', height: '16px' }}
                        />
                      ) : (
                        <img
                          src={expandIcon}
                          alt="expand arrow"
                          // style={{ width: '16px', height: '16px' }}
                        />
                      )
                    }
                  >
                    {/* <Slider
                      style={{ marginBottom: '10px' }}
                      trackStyle={{ backgroundColor: '#2A77F4' }}
                      range
                      step={1}
                      min={minQtySliderValue}
                      max={maxQtySliderValue}
                      value={qtyRange}
                      onChange={onqtyChange}
                    />
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'left',
                        color: '#040606',
                        opacity: 0.5,
                        marginTop: 20,
                      }}
                    >
                      <span style={{ fontSize: 10 }}>{qtyRange[0]}</span>
                      <span>-</span>
                      <span style={{ fontSize: 10 }}>{qtyRange[1]}</span>
                    </div> */}
                    <div style={{ display: 'flex', alignItems: 'end' }}>
                      <Checkbox
                        onChange={(e) => handleOptionSelect(e, 'qty81')}
                        className="ai-filter-checkbox"
                      />
                      <div
                        style={{
                          fontFamily: 'Inter',
                          fontSize: '14px',
                          fontWeight: 700,
                          lineHeight: '20px',
                          letterSpacing: '0.20000000298023224px',
                          textAlign: 'left',
                          textUnderlinePosition: 'from-font',
                          textDecorationSkipInk: 'none',
                          color: '#737373',
                        }}
                      >
                        81
                      </div>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'end' }}>
                      <Checkbox
                        onChange={(e) => handleOptionSelect(e, 'qty95')}
                        className="ai-filter-checkbox"
                      />
                      <div
                        style={{
                          fontFamily: 'Inter',
                          fontSize: '14px',
                          fontWeight: 700,
                          lineHeight: '20px',
                          letterSpacing: '0.20000000298023224px',
                          textAlign: 'left',
                          textUnderlinePosition: 'from-font',
                          textDecorationSkipInk: 'none',
                          color: '#737373',
                        }}
                      >
                        95
                      </div>
                    </div>
                  </SubMenu>
                  {/* <Divider
                    style={{
                      margin: '36px 0px 17px',
                      border: '0.7px solid #E9EDF0',
                    }}
                  /> */}

                  {/* <SubMenu
                style={{
                  marginTop: '-20px',
                  fontSize: '13px',
                }}
                key="sub4"
                title="Language"
              >
                <Menu.Item
                  key="9"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'english')}
                    style={{ marginRight: '10px' }}
                  />
                  English
                </Menu.Item>
                <Menu.Item
                  key="10"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'Spanish')}
                    style={{ marginRight: '10px' }}
                  />
                  Spanish
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} /> */}
                  {/* <SubMenu
                    className="custom-submenu"
                    key="sub5"
                    title="Location"
                    expandIcon={({ isOpen }) =>
                      isOpen ? (
                        <img
                          src={downArrow}
                          alt="collapse arrow"
                          // style={{ width: '16px', height: '16px' }}
                        />
                      ) : (
                        <img
                          src={expandIcon}
                          alt="expand arrow"
                          // style={{ width: '16px', height: '16px' }}
                        />
                      )
                    }
                  >
                    <Menu.Item
                      key="11"
                      style={{
                        padding: '0px',
                        paddingLeft: '0px !important',
                        color: '#A9ABBC',
                      }}
                    >
                      <input
                        type="checkbox"
                        onChange={(e) => handleOptionSelect(e, 'USA')}
                        style={{ marginRight: '10px' }}
                      />
                      USA
                    </Menu.Item>
                    <Menu.Item
                      key="12"
                      style={{
                        padding: '0px',
                        paddingLeft: '0px !important',
                        color: '#A9ABBC',
                      }}
                    >
                      <input
                        type="checkbox"
                        onChange={(e) => handleOptionSelect(e, 'UK')}
                        style={{ marginRight: '10px' }}
                      />
                      UK
                    </Menu.Item>
                  </SubMenu> */}
                  <Divider
                    style={{
                      margin: '25px 0px 43px',
                      border: '0.7px solid #E9EDF0',
                    }}
                  />
                  {/* <SubMenu
                    className="custom-submenu"
                    expandIcon={({ isOpen }) =>
                      isOpen ? (
                        <img
                          src={downArrow}
                          alt="collapse arrow"
                          // style={{ width: '16px', height: '16px' }}
                        />
                      ) : (
                        <img
                          src={expandIcon}
                          alt="expand arrow"
                          // style={{ width: '16px', height: '16px' }}
                        />
                      )
                    }
                    key="sub9"
                    title="Products owned"
                  >
                    <Menu.Item
                      key="13"
                      style={{
                        // marginLeft: '-20px',
                        paddingLeft: '0px',
                        color: '#A9ABBC',
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Checkbox
                          onChange={(e) => handleOptionSelect(e, '4,2,6')}
                          className="ai-filter-checkbox"
                        />
                        <div
                          style={{
                            fontFamily: 'Inter',
                            fontSize: '14px',
                            fontWeight: 700,
                            lineHeight: '24px',
                            letterSpacing: '0.20000000298023224px',
                            textAlign: 'left',
                            textUnderlinePosition: 'from-font',
                            textDecorationSkipInk: 'none',
                            color: '#737373',
                          }}
                        >
                          4,2,6{' '}
                        </div>
                      </div>
                    </Menu.Item>
                    <Menu.Item
                      key="14"
                      style={{
                        // marginLeft: '-20px',
                        paddingLeft: '0px',
                        display: 'flex',
                        alignItems: 'center',
                        color: '#A9ABBC',
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Checkbox
                          onChange={(e) => handleOptionSelect(e, '5,2,6')}
                          className="ai-filter-checkbox"
                        />
                        <div
                          style={{
                            fontFamily: 'Inter',
                            fontSize: '14px',
                            fontWeight: 700,
                            lineHeight: '24px',
                            letterSpacing: '0.20000000298023224px',
                            textAlign: 'left',
                            textUnderlinePosition: 'from-font',
                            textDecorationSkipInk: 'none',
                            color: '#737373',
                          }}
                        >
                          5,2,6
                        </div>
                      </div>
                    </Menu.Item>
                  </SubMenu>
                  <Divider
                    style={{
                      margin: '36px 0px 17px',
                      border: '0.7px solid #E9EDF0',
                    }}
                  /> */}
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '18px',
                    }}
                  >
                    <span
                      style={{
                        fontFamily: 'Inter',
                        fontSize: 16,
                        fontWeight: 700,
                        lineHeight: '24px',
                        letterSpacing: '0.10000000149011612px',
                        textAlign: 'left',
                        textDecorationSkipInk: 'none',
                        color: '#252B42',
                      }}
                    >
                      <IntlMessages id="common.filterPrice" />
                    </span>
                    <Slider
                      style={{
                        // marginBottom: '10px',
                        backgroundColor:
                          'linear-gradient(90deg, #04B8FF 0%, #00D9C0 100%)',
                      }}
                      trackStyle={{}}
                      dotActiveBorderColor="linear-gradient(90deg, #04B8FF 0%, #00D9C0 100%)"
                      step={1}
                      range
                      min={0}
                      max={1000}
                      value={filterPriceRange}
                      className="custom-slider"
                      // value={audienceRange}
                      // onChange={onAudienceChange}
                      onChange={(r) => setFilterPriceRange(r)}
                    />
                    <InputPriceWrapper>
                      <div className="price-range-input-div">
                        <InputNumber
                          min={0}
                          max={1000}
                          className="price-range-input"
                          value={filterPriceRange[0]}
                          onChange={handleMinChange}
                        />
                        <InputNumber
                          min={0}
                          max={1000}
                          className="price-range-input"
                          value={filterPriceRange[1]}
                          onChange={handleMaxChange}
                        />
                      </div>
                    </InputPriceWrapper>
                  </div>

                  {/* <SubMenu
                style={{
                  marginTop: '-20px',
                  fontSize: '13px',
                }}
                key="sub6"
                title="Child age range"
              >
                <Slider
                  style={{ marginTop: '-8px' }}
                  trackStyle={{ backgroundColor: '#2A77F4' }}
                  railStyle={{ boxShadow: '0px 0px 6px #00000029' }}
                  range
                  defaultValue={[6, 9]}
                  onChange={onChange1}
                  onAfterChange={onAfterChange1}
                />
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'left',
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >
                  <span>{sliderRange[0]}</span>
                  <span>-</span>
                  <span>{sliderRange[1]}+</span>
                </div>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} /> */}
                </Menu>
                <div
                  style={{
                    flex: 1,
                    width: '100%',
                    hegith: '100%',
                    marginTop: '49px',
                  }}
                >
                  <Button
                    style={{
                      background: '#09295D',
                      borderRadius: '5px',
                      color: '#fff',
                      width: '100%',
                      height: 44,
                      gap: 10,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    onClick={applyFilter}
                  >
                    <span
                      style={{
                        fontFamily: 'Inter',
                        fontSize: 14,
                        fontWeight: 700,
                        lineHeight: '24px',
                        letterSpacing: '0.20000000298023224px',
                        textAlign: 'center',
                        textUnderlinePosition: 'from-font',
                        textDecorationSkipInk: 'none',
                        color: '#FFFFFF',
                      }}
                    >
                      <IntlMessages id="common.apply" />
                    </span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </Col>
      </Row>
    </MarketingNew1Wrapper>
  );
}
