/* TMPlace Responsive Styles */

/* Base responsive grid container */
.responsiveGrid {
  display: flex;
  width: 100%;
  min-height: 100vh;
}

/* Widget spacing for consistent padding */
.widgetSpacing {
  padding: 16px;
}

/* Header controls responsive styling */
.headerControls {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #fff;
}

/* Chart container responsive styling */
.chartContainer {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

/* Mobile breakpoint (< 768px) */
@media (max-width: 767px) {
  .responsiveGrid {
    flex-direction: column;
  }

  .widgetSpacing {
    padding: 12px;
  }

  .headerControls {
    padding: 16px;
    flex-wrap: wrap;
    gap: 12px;
  }

  .chartContainer {
    margin: 8px 0;
    border-radius: 6px;
  }

  /* Mobile-specific card grid */
  .mobile-card-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  /* Mobile touch targets */
  .touch-target {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Mobile form elements */
  .mobile-form-input {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .mobile-form-button {
    min-height: 48px;
    width: 100%;
    font-size: 16px;
    font-weight: 500;
  }

  /* Mobile popup adjustments */
  .mobile-popup {
    width: 90% !important;
    max-width: 100% !important;
    max-height: 90vh !important;
    overflow-y: auto;
    padding: 20px !important;
  }

  /* Mobile filter sidebar */
  .mobile-filter {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    z-index: 1000 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
  }

  .mobile-filter-content {
    width: 80% !important;
    height: 100vh !important;
    background: #fff !important;
    padding: 16px !important;
    overflow-y: auto !important;
  }
}

/* Tablet breakpoint (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .responsiveGrid {
    flex-direction: row;
  }

  .widgetSpacing {
    padding: 16px;
  }

  .headerControls {
    padding: 18px;
  }

  .chartContainer {
    margin: 12px 0;
    border-radius: 8px;
  }

  /* Tablet card grid */
  .tablet-card-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 16px;
  }

  /* Tablet form elements */
  .tablet-form-input {
    min-height: 40px;
    font-size: 14px;
  }

  .tablet-form-button {
    min-height: 44px;
    font-size: 14px;
  }

  /* Tablet popup adjustments */
  .tablet-popup {
    width: 70% !important;
    padding: 30px !important;
  }
}

/* Desktop breakpoint (>= 1024px) */
@media (min-width: 1024px) {
  .responsiveGrid {
    flex-direction: row;
  }

  .widgetSpacing {
    padding: 20px;
  }

  .headerControls {
    padding: 20px;
  }

  .chartContainer {
    margin: 16px 0;
    border-radius: 8px;
  }

  /* Desktop card grid */
  .desktop-card-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    padding: 20px 10px;
  }

  /* Desktop form elements */
  .desktop-form-input {
    min-height: 36px;
    font-size: 14px;
  }

  .desktop-form-button {
    min-height: 40px;
    font-size: 14px;
  }

  /* Desktop popup adjustments */
  .desktop-popup {
    width: auto !important;
    padding: 50px !important;
  }
}

/* Utility classes for responsive behavior */
.flex-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.grid-responsive {
  display: grid;
  gap: 12px;
}

.text-responsive {
  font-size: clamp(14px, 2.5vw, 18px);
  line-height: 1.4;
}

.spacing-responsive {
  margin: clamp(8px, 2vw, 16px) 0;
  padding: clamp(8px, 2vw, 16px);
}

/* Animation for smooth transitions */
.responsive-transition {
  transition: all 0.3s ease-in-out;
}

/* Focus states for accessibility */
.touch-target:focus,
.mobile-form-input:focus,
.tablet-form-input:focus,
.desktop-form-input:focus {
  outline: 2px solid #2a77f4;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .headerControls,
  .chartContainer,
  .widgetSpacing {
    border: 1px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .responsive-transition {
    transition: none;
  }
}

/* List Item Responsive Styles */
.list-item-container {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px;
  width: 100%;
  background: #fff;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}

.list-item-container:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.list-item-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
}

.list-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0; /* Prevents flex item from overflowing */
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.list-item-title {
  font-weight: bold;
  font-size: 16px;
  color: #2a77f4;
  font-family: 'Rubik', sans-serif;
  margin: 0;
  flex: 1;
}

.list-item-price {
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
  color: #333;
  margin: 0;
  font-weight: 500;
}

.list-item-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #a9abbc;
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
  gap: 12px;
}

.list-item-info-label {
  flex: 1;
  margin: 0;
}

.list-item-info-value {
  font-weight: 500;
  color: #666;
  margin: 0;
}

.list-item-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.list-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  min-height: 24px;
}

.list-item-star {
  color: #ffc107;
  font-size: 16px;
}

.list-item-menu {
  color: #a9abbc;
  font-size: 20px;
}

/* Mobile responsive styles for list items */
@media (max-width: 767px) {
  .list-item-container {
    flex-direction: column;
    align-items: stretch;
    padding: 12px;
    gap: 12px;
  }

  .list-item-image {
    width: 100%;
    height: 200px;
    align-self: center;
    max-width: 300px;
  }

  .list-item-content {
    gap: 12px;
  }

  .list-item-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .list-item-title {
    font-size: 18px;
    text-align: center;
  }

  .list-item-price {
    font-size: 18px;
    text-align: center;
    font-weight: 600;
  }

  .list-item-info-row {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
    text-align: center;
  }

  .list-item-info-label {
    font-size: 14px;
  }

  .list-item-info-value {
    font-size: 16px;
    font-weight: 600;
  }

  .list-item-actions {
    justify-content: center;
    margin-top: 8px;
  }

  .list-item-icon {
    min-width: 44px;
    min-height: 44px;
  }
}

/* Tablet responsive styles for list items */
@media (min-width: 768px) and (max-width: 1023px) {
  .list-item-container {
    padding: 14px;
    gap: 14px;
  }

  .list-item-image {
    width: 100px;
    height: 100px;
  }

  .list-item-title {
    font-size: 17px;
  }

  .list-item-price {
    font-size: 17px;
  }

  .list-item-info-row {
    font-size: 13px;
  }

  .list-item-icon {
    min-width: 32px;
    min-height: 32px;
  }
}

/* Desktop responsive styles for list items */
@media (min-width: 1024px) {
  .list-item-container {
    padding: 16px;
    gap: 16px;
  }

  .list-item-image {
    width: 80px;
    height: 80px;
  }

  .list-item-title {
    font-size: 16px;
  }

  .list-item-price {
    font-size: 16px;
  }

  .list-item-info-row {
    font-size: 12px;
  }
}
