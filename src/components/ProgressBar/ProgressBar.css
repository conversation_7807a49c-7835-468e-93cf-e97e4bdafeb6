/* ProgressBar Component Responsive Styles */

.chartLegend {
  width: 100%;
  margin: 0;
  padding: 0;
}

.legendRow {
  display: block;
  margin: 3px 0;
  margin-bottom: 15px;
  width: 100%;
}

.legendText {
  font-family: 'Rubik', sans-serif;
  font-size: 12px;
  color: #666;
  font-weight: 400;
  line-height: 1.4;
}

.progress {
  padding: 0;
  width: 100%;
  height: 2px;
  overflow: hidden;
  background: #B6EAFF;
  border-radius: 6px;
  position: relative;
  margin-top: 4px;
}

.bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(4, 184, 255, 1);
  border-radius: 6px;
  transition: width 0.3s ease-in-out;
}

/* Mobile responsive styles */
@media (max-width: 767px) {
  .legendRow {
    margin-bottom: 18px;
  }
  
  .legendText {
    font-size: 14px;
  }
  
  .progress {
    height: 3px;
    margin-top: 6px;
  }
}

/* Tablet responsive styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .legendRow {
    margin-bottom: 16px;
  }
  
  .legendText {
    font-size: 13px;
  }
  
  .progress {
    height: 2.5px;
    margin-top: 5px;
  }
}

/* Desktop responsive styles */
@media (min-width: 1024px) {
  .legendRow {
    margin-bottom: 15px;
  }
  
  .legendText {
    font-size: 12px;
  }
  
  .progress {
    height: 2px;
    margin-top: 4px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .progress {
    border: 1px solid #000;
  }
  
  .bar {
    background: #000;
  }
  
  .legendText {
    color: #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .bar {
    transition: none;
  }
}

/* Focus states for accessibility */
.legendRow:focus-within {
  outline: 2px solid #2A77F4;
  outline-offset: 2px;
  border-radius: 4px;
}
