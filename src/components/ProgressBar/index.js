/* eslint-disable consistent-return */
import { isEmpty } from 'lodash';
import React from 'react';
import formatValue from '../utility/commonFunction';
import './ProgressBar.css';

function progressBar({ array }) {
  // Validate input array
  if (isEmpty(array) || !Array.isArray(array)) {
    return null;
  }

  // Extract all states from the array
  const allStates = array.flatMap((item) => item?.states || []);
  // Validate that we have states to display
  if (!allStates || allStates.length === 0) {
    return null;
  }

  // Calculate total count for percentage calculation
  const totalCount = allStates.reduce((sum, state) => {
    const count = Number(state?.count) || 0;
    return sum + Math.max(0, count); // Ensure non-negative values
  }, 0);

  // If total is 0, don't render progress bars
  if (totalCount === 0) {
    return (
      <div className="chartLegend">
        {allStates.map((value) => (
          <div
            key={`progress-zero-${
              value?.state || `unknown-${Date.now()}-${Math.random()}`
            }`}
            className="legendRow"
            style={{ display: 'block', margin: 3, marginBottom: 15 }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span className="legendText">{value?.state || 'Unknown'}</span>
              <span className="legendText">0</span>
            </div>
            <div
              className="progress"
              style={{
                padding: 0,
                width: '100%',
                height: '2px',
                overflow: 'hidden',
                background: '#B6EAFF',
                borderRadius: '6px',
                position: 'relative',
              }}
            >
              <div
                className="bar"
                style={{
                  width: '0%',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  height: '100%',
                  background: 'rgba(4, 184, 255, 1)',
                  borderRadius: '6px',
                  transition: 'width 0.3s ease-in-out',
                }}
              />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="chartLegend">
      {allStates.map((value, index) => {
        const count = Number(value?.count) || 0;
        const safeCount = Math.max(0, count); // Ensure non-negative
        const percentage = totalCount > 0 ? (safeCount / totalCount) * 100 : 0;
        const widthPercentage = Math.min(100, Math.max(0, percentage)); // Clamp between 0-100%

        return (
          <div
            key={`progress-item-${value?.state || index}`}
            className="legendRow"
            style={{ display: 'block', margin: 3, marginBottom: 15 }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span className="legendText">{value?.state || 'Unknown'}</span>
              <span className="legendText">{formatValue(safeCount) || 0}</span>
            </div>

            <div
              className="progress"
              style={{
                padding: 0,
                width: '100%',
                height: '2px',
                overflow: 'hidden',
                background: '#B6EAFF',
                borderRadius: '6px',
                position: 'relative',
              }}
            >
              <div
                className="bar"
                style={{
                  width: `${widthPercentage}%`,
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  height: '100%',
                  background: 'rgba(4, 184, 255, 1)',
                  borderRadius: '6px',
                  transition: 'width 0.3s ease-in-out',
                  minWidth: widthPercentage > 0 ? '1%' : '0%',
                }}
              />
            </div>
          </div>
        );
      })}
    </div>
  );
}

export default progressBar;
